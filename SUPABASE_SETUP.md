# 🚀 Supabase Real-time Setup Guide

This guide will help you set up Supabase for real-time messaging and notifications while keeping MySQL as your primary database.

## 📋 Prerequisites

1. Supabase project created
2. Environment variables configured
3. Supabase credentials available

## 🔧 Environment Variables

Add these to your `.env.local` file:

```env
# Supabase Configuration (Real-time Features Only)
NEXT_PUBLIC_SUPABASE_URL=https://xzrqnkapgxfmobrioang.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh6cnFua2FwZ3hmbW9icmlvYW5nIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NDY0NDAsImV4cCI6MjA2ODQyMjQ0MH0.Hz3YNL4V4-GQuAp4FcoSFffiNK9cOuMhoP4UTqcrmMk
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh6cnFua2FwZ3hmbW9icmlvYW5nIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1Mjg0NjQ0MCwiZXhwIjoyMDY4NDIyNDQwfQ.SH1uiO9nRlM3aVDj-v1jCNO260LdRgTjws94x6F5uRo
```

## 🗄️ Database Setup

### Option 1: Automatic Setup (Recommended)

Run the setup script:

```bash
npm run setup-supabase
```

### Option 2: Manual Setup

1. Go to your Supabase Dashboard
2. Navigate to SQL Editor
3. Copy and paste the contents of `supabase/setup-realtime-tables.sql`
4. Execute the SQL

## 📊 Tables Created

The setup creates these minimal tables for real-time features:

### `messages_realtime`
- Real-time message broadcasting
- Temporary storage (7 days retention)
- Links to MySQL messages via `mysql_message_id`

### `notifications_realtime`
- Instant notification delivery
- 30 days retention
- Links to MySQL notifications via `mysql_notification_id`

### `user_presence`
- Online/offline status
- Typing indicators
- Last seen timestamps

## 🔒 Security Features

- Row Level Security (RLS) enabled
- Appropriate policies for data access
- Performance indexes
- Automatic cleanup functions

## 🔄 Data Flow

```
MySQL (Primary Storage) → API Routes → Supabase (Real-time) → Client Components
```

1. **Messages**: Saved to MySQL → Broadcasted via Supabase → Auto-cleanup after 7 days
2. **Notifications**: Persisted in MySQL → Instant delivery via Supabase → Cleanup after 30 days
3. **Presence**: Only in Supabase (temporary data)

## ✅ Verification

After setup, verify the tables exist:

1. Go to Supabase Dashboard → Table Editor
2. Check for these tables:
   - `messages_realtime`
   - `notifications_realtime`
   - `user_presence`

## 🚀 Next Steps

1. ✅ Tables created
2. 🔄 Implement real-time services
3. 🔗 Update message components
4. 📱 Add notification handlers
5. 🧪 Test real-time functionality

## 🛠️ Troubleshooting

### Common Issues:

1. **RLS Policies**: If you get permission errors, check RLS policies
2. **Realtime**: Ensure tables are added to `supabase_realtime` publication
3. **Environment**: Verify all environment variables are set correctly

### Debug Commands:

```sql
-- Check if tables exist
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';

-- Check realtime publication
SELECT * FROM pg_publication_tables WHERE pubname = 'supabase_realtime';

-- Test connection
SELECT NOW();
```

## 📞 Support

If you encounter issues:
1. Check Supabase logs in Dashboard
2. Verify environment variables
3. Test connection with the debug commands above
