/**
 * Real-time Messages Hook
 * Handles real-time message subscriptions using Supabase
 */

import { useEffect, useState, useCallback, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { connectionManager, RealtimeMessage, isRealtimeEnabled } from '@/lib/supabase/client';
import { updatePresence } from '@/lib/supabase/syncService';
import { errorHandler, withErrorHandling } from '@/lib/supabase/errorHandler';
import { connectionRecovery } from '@/lib/supabase/connectionRecovery';
import { fallbackService } from '@/lib/supabase/fallbackService';

interface UseRealtimeMessagesProps {
  userId?: string;
  onNewMessage?: (message: RealtimeMessage) => void;
  onTypingChange?: (typingUsers: string[]) => void;
  onPresenceChange?: (onlineUsers: string[]) => void;
}

interface RealtimeMessagesState {
  isConnected: boolean;
  isTyping: boolean;
  typingUsers: string[];
  onlineUsers: string[];
  connectionError: string | null;
  isRecovering: boolean;
  fallbackMode: boolean;
  retryCount: number;
}

export function useRealtimeMessages({
  userId,
  onNewMessage,
  onTypingChange,
  onPresenceChange,
}: UseRealtimeMessagesProps = {}) {
  const { data: session } = useSession();
  const [state, setState] = useState<RealtimeMessagesState>({
    isConnected: false,
    isTyping: false,
    typingUsers: [],
    onlineUsers: [],
    connectionError: null,
    isRecovering: false,
    fallbackMode: false,
    retryCount: 0,
  });

  const typingTimeoutRef = useRef<NodeJS.Timeout>();
  const currentUserIdRef = useRef<string>();

  // Update current user ID
  useEffect(() => {
    if (session?.user?.id) {
      currentUserIdRef.current = session.user.id;
    }
  }, [session?.user?.id]);

  // Setup connection recovery listener
  useEffect(() => {
    if (!isRealtimeEnabled()) return;

    const unsubscribe = connectionRecovery.addListener((connectionState) => {
      setState(prev => ({
        ...prev,
        isRecovering: connectionState.isRecovering,
        fallbackMode: connectionState.fallbackMode,
        retryCount: connectionState.reconnectAttempts,
      }));
    });

    // Initialize connection recovery
    connectionRecovery.initialize();

    return unsubscribe;
  }, []);

  // Setup fallback mode listeners
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleFallbackMessage = (event: CustomEvent) => {
      onNewMessage?.(event.detail.message);
    };

    window.addEventListener('fallback-message', handleFallbackMessage as EventListener);

    return () => {
      window.removeEventListener('fallback-message', handleFallbackMessage as EventListener);
    };
  }, [onNewMessage]);

  // Subscribe to real-time messages
  useEffect(() => {
    if (!isRealtimeEnabled() || !session?.user?.id) {
      return;
    }

    const currentUserId = session.user.id;

    const setupConnection = async () => {
      try {
        // Subscribe to messages for current user with error handling
        const messageChannel = await withErrorHandling(
          () => connectionManager.subscribeToMessages(
            currentUserId,
            (message: RealtimeMessage) => {
              console.log('📨 New real-time message received:', message);
              onNewMessage?.(message);
            }
          ),
          {
            operation: 'subscribe_to_messages',
            userId: currentUserId,
            timestamp: new Date(),
          }
        );

      // Subscribe to presence updates
      const presenceChannel = connectionManager.subscribeToPresence(
        currentUserId,
        (presenceList) => {
          const onlineUserIds = presenceList
            .filter(p => p.status === 'online')
            .map(p => p.user_id);
          
          const typingUserIds = presenceList
            .filter(p => p.typing_in && p.user_id !== currentUserId)
            .map(p => p.user_id);

          setState(prev => ({
            ...prev,
            onlineUsers: onlineUserIds,
            typingUsers: typingUserIds,
            isConnected: true,
            connectionError: null,
          }));

          onPresenceChange?.(onlineUserIds);
          onTypingChange?.(typingUserIds);
        }
      );

        // Update user presence to online with error handling
        await withErrorHandling(
          () => updatePresence(currentUserId, 'online'),
          {
            operation: 'update_presence_online',
            userId: currentUserId,
            timestamp: new Date(),
          }
        );

      setState(prev => ({ ...prev, isConnected: true, connectionError: null }));

      // Cleanup function
      return () => {
        connectionManager.unsubscribe(`messages:${currentUserId}`);
        connectionManager.unsubscribe('presence:global');
        
        // Set user offline when component unmounts
        updatePresence(currentUserId, 'offline').catch(error => {
          console.error('Failed to update offline presence:', error);
        });
      };
      } catch (error) {
        errorHandler.handleError(error, {
          operation: 'setup_realtime_subscriptions',
          userId: currentUserId,
          timestamp: new Date(),
        });

        setState(prev => ({
          ...prev,
          isConnected: false,
          connectionError: String(error),
        }));

        // Enter fallback mode if connection fails
        fallbackService.enterFallbackMode();
      }
    };

    setupConnection();
  }, [session?.user?.id, onNewMessage, onPresenceChange, onTypingChange]);

  // Start typing indicator
  const startTyping = useCallback((conversationId?: string) => {
    if (!isRealtimeEnabled() || !currentUserIdRef.current) {
      return;
    }

    setState(prev => ({ ...prev, isTyping: true }));

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Update presence with typing status
    updatePresence(currentUserIdRef.current, 'online', conversationId).catch(error => {
      console.error('Failed to update typing presence:', error);
    });

    // Auto-stop typing after 3 seconds
    typingTimeoutRef.current = setTimeout(() => {
      stopTyping();
    }, 3000);
  }, []);

  // Stop typing indicator
  const stopTyping = useCallback(() => {
    if (!isRealtimeEnabled() || !currentUserIdRef.current) {
      return;
    }

    setState(prev => ({ ...prev, isTyping: false }));

    // Clear timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = undefined;
    }

    // Update presence without typing status
    updatePresence(currentUserIdRef.current, 'online').catch(error => {
      console.error('Failed to clear typing presence:', error);
    });
  }, []);

  // Mark user as away
  const setAway = useCallback(() => {
    if (!isRealtimeEnabled() || !currentUserIdRef.current) {
      return;
    }

    updatePresence(currentUserIdRef.current, 'away').catch(error => {
      console.error('Failed to update away presence:', error);
    });
  }, []);

  // Mark user as online
  const setOnline = useCallback(() => {
    if (!isRealtimeEnabled() || !currentUserIdRef.current) {
      return;
    }

    updatePresence(currentUserIdRef.current, 'online').catch(error => {
      console.error('Failed to update online presence:', error);
    });
  }, []);

  // Get connection status
  const getConnectionStatus = useCallback(() => {
    return connectionManager.getConnectionStatus();
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  return {
    // State
    isConnected: state.isConnected && isRealtimeEnabled(),
    isTyping: state.isTyping,
    typingUsers: state.typingUsers,
    onlineUsers: state.onlineUsers,
    connectionError: state.connectionError,
    isRealtimeEnabled: isRealtimeEnabled(),

    // Actions
    startTyping,
    stopTyping,
    setAway,
    setOnline,
    getConnectionStatus,
  };
}
